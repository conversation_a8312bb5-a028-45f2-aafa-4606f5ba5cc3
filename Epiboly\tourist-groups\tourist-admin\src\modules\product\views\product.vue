<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert">
			<!-- 产品价格表自定义插槽 -->
			<template #slot-price-table="{ scope }">
				<div class="price-table">
					<!-- 确保数据结构初始化 -->
					<div v-if="!scope.priceTable || (!scope.priceTable.headers && !Array.isArray(scope.priceTable))"
						style="margin-bottom: 10px;">
						<el-button type="primary" size="small" @click="() => initializePriceTable(scope)">
							初始化价格表
						</el-button>
					</div>

					<!-- 新的价格表结构 -->
					<div v-else-if="scope.priceTable.headers">
						<!-- 表头编辑区域 -->
						<div style="margin-bottom: 15px;">
							<div
								style="margin-bottom: 10px; font-weight: bold; display: flex; justify-content: space-between; align-items: center;">
								<span>价格表管理</span>
								<div>
									<el-button type="success" size="small"
										:disabled="scope.priceTable.headers.length >= 10"
										@click="() => addColumnSimple(scope)">
										添加列 ({{ scope.priceTable.headers.length }}/10)
									</el-button>
									<el-button type="primary" size="small" @click="() => addRow(scope)">
										添加行
									</el-button>
								</div>
							</div>

							<!-- 列名编辑区域 -->
							<div style="margin-bottom: 10px;">
								<div style="font-weight: bold; margin-bottom: 5px;">列设置：</div>
								<div style="display: flex; flex-wrap: wrap; gap: 10px; align-items: center;">
									<div v-for="(header, headerIndex) in scope.priceTable.headers" :key="header.key"
										style="display: flex; align-items: center; background: #f0f9ff; border: 1px solid #e0f2fe; border-radius: 4px; padding: 5px;">
										<el-input v-model="header.label" placeholder="列名" size="small"
											style="width: 100px;" />
										<el-button type="danger" size="small" text
											@click="() => deleteColumn(scope, headerIndex)"
											style="margin-left: 5px; padding: 2px 5px;">
											×
										</el-button>
									</div>
								</div>
							</div>

							<!-- 数据表格 -->
							<el-table :data="scope.priceTable.rows" border size="small" style="width: 100%">
								<el-table-column label="#" align="center" width="50">
									<template #default="{ $index }">
										<span>{{ $index + 1 }}</span>
									</template>
								</el-table-column>
								<el-table-column v-for="header in scope.priceTable.headers" :key="header.key"
									:label="header.label" align="center">
									<template #default="{ row, $index }">
										<el-input v-model="row[header.key]" :placeholder="header.label" size="small" />
									</template>
								</el-table-column>
								<el-table-column label="操作" align="center" width="80">
									<template #default="{ $index }">
										<el-button type="danger" size="small" text
											@click="() => deleteRow(scope, $index)">
											删除
										</el-button>
									</template>
								</el-table-column>
							</el-table>
						</div>
					</div>

					<!-- 兼容旧格式 -->
					<div v-else-if="Array.isArray(scope.priceTable)">
						<div style="margin-bottom: 10px; color: #E6A23C;">
							<i class="el-icon-warning"></i> 检测到旧版格式，建议转换为新格式以支持动态列管理
							<el-button type="warning" size="small" style="margin-left: 10px;"
								@click="() => convertOldFormat(scope)">
								转换为新格式
							</el-button>
						</div>

						<el-table :data="scope.priceTable" border size="small" style="width: 100%" :show-header="false">
							<el-table-column align="center">
								<template #default="{ row, $index }">
									<el-input v-model="row.peopleCount" placeholder="如：10人" size="small" clearable />
								</template>
							</el-table-column>
							<el-table-column align="center">
								<template #default="{ row, $index }">
									<el-input-number v-model="row.doubleRoom" :min="0" :precision="0" placeholder="价格"
										size="small" :controls="false" style="width: 100%" />
								</template>
							</el-table-column>
							<el-table-column align="center">
								<template #default="{ row, $index }">
									<el-input-number v-model="row.singleRoom" :min="0" :precision="0" placeholder="价格"
										size="small" :controls="false" style="width: 100%" />
								</template>
							</el-table-column>
							<el-table-column align="center">
								<template #default="{ row, $index }">
									<el-input-number v-model="row.extraRoom" :min="0" :precision="0" placeholder="价格"
										size="small" :controls="false" style="width: 100%" />
								</template>
							</el-table-column>
							<el-table-column width="80" align="center">
								<template #default="{ row, $index }">
									<el-button type="danger" size="small" text @click="() => {
										if (scope.priceTable && scope.priceTable.length > 0) {
											scope.priceTable.splice($index, 1);
										}
									}">
										删除
									</el-button>
								</template>
							</el-table-column>
						</el-table>

						<div style="margin-top: 10px; text-align: right">
							<el-button type="primary" size="small" @click="() => {
								if (!scope.priceTable) {
									scope.priceTable = [];
								}
								scope.priceTable.push({
									peopleCount: '',
									doubleRoom: 0,
									singleRoom: 0,
									extraRoom: 0
								});
							}">
								添加价格行
							</el-button>
						</div>
					</div>
				</div>
			</template>

			<!-- 自定义行程信息插槽 -->
			<template #slot-custom-itinerary="{ scope }">
				<div class="custom-itinerary">
					<el-table :data="scope.customItineraryFields" border size="small" style="width: 100%">
						<el-table-column label="字段名称" align="center">
							<template #default="{ row, $index }">
								<el-input v-model="row.key" placeholder="请输入字段名称" size="small" clearable />
							</template>
						</el-table-column>
						<el-table-column label="字段值" align="center">
							<template #default="{ row, $index }">
								<el-input v-model="row.value" placeholder="请输入字段值" size="small" clearable />
							</template>
						</el-table-column>
						<el-table-column width="80" align="center" label="操作">
							<template #default="{ row, $index }">
								<el-button type="danger" size="small" text @click="() => {
									if (scope.customItineraryFields && scope.customItineraryFields.length > 0) {
										scope.customItineraryFields.splice($index, 1);
									}
								}">
									删除
								</el-button>
							</template>
						</el-table-column>
					</el-table>

					<div style="margin-top: 10px; text-align: right">
						<el-button type="primary" size="small" @click="() => {
							if (!scope.customItineraryFields) {
								scope.customItineraryFields = [];
							}
							scope.customItineraryFields.push({
								key: '',
								value: ''
							});
						}">
							添加字段
						</el-button>
					</div>
				</div>
			</template>

			<!-- 预定须知自定义字段插槽 -->
			<template #slot-custom-booking-notice="{ scope }">
				<div class="custom-booking-notice">
					<el-table :data="scope.customBookingNoticeFields" border size="small" style="width: 100%">
						<el-table-column label="字段名称" align="center">
							<template #default="{ row, $index }">
								<el-input v-model="row.key" placeholder="请输入字段名称" size="small" clearable />
							</template>
						</el-table-column>
						<el-table-column label="字段值" align="center">
							<template #default="{ row, $index }">
								<el-input v-model="row.value" type="textarea" :rows="2" placeholder="请输入字段值"
									size="small" />
							</template>
						</el-table-column>
						<el-table-column width="80" align="center" label="操作">
							<template #default="{ row, $index }">
								<el-button type="danger" size="small" text @click="() => {
									if (scope.customBookingNoticeFields && scope.customBookingNoticeFields.length > 0) {
										scope.customBookingNoticeFields.splice($index, 1);
									}
								}">
									删除
								</el-button>
							</template>
						</el-table-column>
					</el-table>

					<div style="margin-top: 10px; text-align: right">
						<el-button type="primary" size="small" @click="() => {
							if (!scope.customBookingNoticeFields) {
								scope.customBookingNoticeFields = [];
							}
							scope.customBookingNoticeFields.push({
								key: '',
								value: ''
							});
						}">
							添加字段
						</el-button>
					</div>
				</div>
			</template>
		</cl-upsert>
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "product-product",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { reactive, onMounted, h, nextTick } from "vue";

const { service } = useCool();
const { t } = useI18n();

// 选项
const options = reactive({
	score: [
		{ label: "1分", value: 1 },
		{ label: "2分", value: 2 },
		{ label: "3分", value: 3 },
		{ label: "4分", value: 4 },
		{ label: "5分", value: 5 },
	],
	productType: [
		{ label: "预定团", value: 0, type: "primary" },
		{ label: "散拼团", value: 1, type: "success" },
	],
	isHot: [
		{ label: "否", value: 0, type: "danger" },
		{ label: "是", value: 1, type: "success" },
	],
	isPopular: [
		{ label: "否", value: 0, type: "danger" },
		{ label: "是", value: 1, type: "success" },
	],
	includePickup: [
		{ label: "否", value: 0, type: "danger" },
		{ label: "是", value: 1, type: "success" },
	],
	status: [
		{ label: "下架", value: 0, type: "danger" },
		{ label: "上架", value: 1, type: "success" },
	],
	itineraryType: [
		{ label: "预设字段", value: 0, type: "primary" },
		{ label: "自定义字段", value: 1, type: "success" },
	],
	bookingNoticeType: [
		{ label: "预设字段", value: 0, type: "primary" },
		{ label: "自定义字段", value: 1, type: "success" },
	],
	categoryOptions: [] as Array<{ label: string, value: number }>, // 改为动态加载
	serviceGuaranteeOptions: [
		"入园保障", "快速入园", "随时退款", "7*24小时客服"
	]
});

// 加载分类列表
const loadCategories = async () => {
	try {
		console.log('开始加载分类列表...');
		const res = await service.product.category.list();
		console.log('分类列表响应:', res);

		// Cool框架的API响应格式：{code: 1000, message: "success", data: [...]}
		let categoryData: any[] = [];
		const response = res as any;

		if (response && response.code === 1000 && Array.isArray(response.data)) {
			// 从API响应的data字段中提取分类数据
			categoryData = response.data;
		} else if (res && Array.isArray(res)) {
			// 如果直接返回数组
			categoryData = res as any[];
		} else {
			console.warn('分类列表响应格式不正确:', res);
			options.categoryOptions = [];
			return;
		}

		// 过滤并转换为选项格式
		const newCategoryOptions = categoryData
			.filter((item: any) => item.name && item.id !== undefined && item.status === 1)
			.map((item: any) => ({
				label: item.name,
				value: item.id
			}));

		// 更新响应式数据
		options.categoryOptions.splice(0, options.categoryOptions.length, ...newCategoryOptions);

		console.log('处理后的分类选项:', options.categoryOptions);
		console.log('分类选项数量:', options.categoryOptions.length);

		// 强制触发Vue响应式更新
		nextTick(() => {
			console.log('强制更新后的分类选项:', options.categoryOptions);
			// 尝试手动更新Upsert组件的配置
			if (Upsert.value) {
				try {
					Upsert.value.refresh?.();
					console.log('已尝试刷新Upsert组件');
				} catch (e) {
					console.log('Upsert组件刷新失败，这是正常的');
				}
			}
		});

	} catch (error: any) {
		console.error('加载分类列表失败:', error);
		options.categoryOptions = [];
	}
};

// cl-upsert
const Upsert = useUpsert({
	items: [
		// ============ 基本信息卡片 ============
		{
			component: {
				name: "cl-form-card",
				props: {
					label: "基本信息",
					expand: true
				}
			},
			children: [
				{
					label: "产品名称",
					prop: "name",
					component: { name: "el-input", props: { clearable: true, placeholder: "请输入产品名称" } },
					span: 12,
					required: true,
				},
				{
					label: "产品编号",
					prop: "productCode",
					component: { name: "el-input", props: { clearable: true, placeholder: "请输入产品编号" } },
					span: 12,
					required: true,
				},
				{
					label: "产品副标题",
					prop: "subtitle",
					component: { name: "el-input", props: { clearable: true, placeholder: "请输入产品副标题" } },
					span: 12,
				},
				{
					label: "产品类型",
					prop: "productType",
					component: {
						name: "el-radio-group",
						options: options.productType,
					},
					value: 0,
					span: 12,
				},
				{
					label: "成团人数",
					prop: "groupSize",
					component: {
						name: "el-input-number",
						props: {
							min: 1,
							max: 500,
							precision: 0,
							placeholder: "请输入成团人数"
						}
					},
					value: 5,
					span: 12,
					// 只有当产品类型为散拼团(1)且为新增模式时才显示
					hidden: ({ scope }) => scope.productType !== 1,
				},
				{
					label: "分类",
					prop: "categoryIds",
					component: {
						name: "el-select",
						props: {
							multiple: true,
							filterable: true,
							placeholder: "请选择分类"
						},
						options: options.categoryOptions
					},
					span: 12,
				},
				{
					label: "是否热门",
					prop: "isHot",
					component: {
						name: "el-radio-group",
						options: options.isHot,
					},
					value: 0,
					span: 12,
				},
				{
					label: "产品介绍",
					prop: "introduce",
					component: {
						name: "el-input",
						props: { type: "textarea", rows: 3, placeholder: "请输入产品介绍" },
					},
				},
				{
					label: "封面图",
					prop: "cover",
					component: { name: "cl-upload" },
					span: 12,
				},
				{
					label: "产品图片",
					prop: "images",
					component: { name: "cl-upload", props: { multiple: true, limit: 10 } },
					span: 12,
				},
				{
					label: '日期范围',
					prop: 'dateRange',
					component: {
						name: 'el-date-picker',
						props: {
							type: 'daterange',
							rangeSeparator: '至',
							startPlaceholder: '开始日期',
							endPlaceholder: '结束日期',
							format: 'YYYY-MM-DD',
							valueFormat: 'YYYY-MM-DD'
						}
					},
					span: 24,
				},
			]
		},

		// ============ 价格评分信息卡片 ============
		{
			component: {
				name: "cl-form-card",
				props: {
					label: "价格评分信息",
					expand: true
				}
			},
			children: [
				{
					label: "价格",
					prop: "price",
					hook: "number",
					component: { name: "el-input-number", props: { min: 0, precision: 2 } },
					span: 12,
					required: true,
				},
				{
					label: "原价",
					prop: "originalPrice",
					hook: "number",
					component: { name: "el-input-number", props: { min: 0, precision: 2 } },
					span: 12,
				},
				{
					label: "评分",
					prop: "score",
					component: { name: "cl-select", props: { options: options.score } },
					value: 5,
					span: 12,
				},
				{
					label: "排序",
					prop: "sort",
					hook: "number",
					component: { name: "el-input-number", props: { min: 0 } },
					value: 0,
					span: 12,
				},
				{
					label: "产品价格表",
					prop: "priceTable",
					component: {
						name: "slot-price-table"
					},
					value: {
						headers: [
							{ key: "peopleCount", label: "人数", type: "text" },
							{ key: "doubleRoom", label: "双人间", type: "text" },
							{ key: "singleRoom", label: "单人间", type: "text" },
							{ key: "extraRoom", label: "加床", type: "text" }
						],
						rows: [
							{ peopleCount: "10人", doubleRoom: "643", singleRoom: "857", extraRoom: "0" },
							{ peopleCount: "16人", doubleRoom: "551", singleRoom: "765", extraRoom: "0" },
							{ peopleCount: "25人", doubleRoom: "507", singleRoom: "721", extraRoom: "0" }
						]
					}
				},
			]
		},

		// ============ 行程信息卡片 ============
		{
			component: {
				name: "cl-form-card",
				props: {
					label: "行程基本信息",
					expand: true
				}
			},
			children: [
				{
					label: "行程信息类型",
					prop: "itineraryType",
					component: {
						name: "el-radio-group",
						options: options.itineraryType,
					},
					value: 0,
					span: 24,
				},
				{
					label: "出发城市",
					prop: "city",
					component: { name: "el-input", props: { clearable: true, placeholder: "请输入城市" } },
					span: 12,
					hidden: ({ scope }) => scope.itineraryType === 1,
				},
				{
					label: "目的地",
					prop: "destination",
					component: { name: "el-input", props: { clearable: true, placeholder: "请输入目的地" } },
					span: 12,
					hidden: ({ scope }) => scope.itineraryType === 1,
				},
				{
					label: "行程天数",
					prop: "days",
					hook: "number",
					component: { name: "el-input-number", props: { min: 1 } },
					value: 1,
					span: 12,
					hidden: ({ scope }) => scope.itineraryType === 1,
				},
				{
					label: "交通信息",
					prop: "transport",
					component: { name: "el-input", props: { clearable: true, placeholder: "如：汽车往返空调大巴" } },
					span: 12,
					hidden: ({ scope }) => scope.itineraryType === 1,
				},
				{
					label: "活动信息",
					prop: "activityInfo",
					component: { name: "el-input", props: { clearable: true, placeholder: "如：活动信息" } },
					span: 12,
					hidden: ({ scope }) => scope.itineraryType === 1,
				},
				{
					label: "自定义行程字段",
					prop: "customItineraryFields",
					component: {
						name: "slot-custom-itinerary"
					},
					value: [],
					hidden: ({ scope }) => scope.itineraryType === 0,
				},
			]
		},

		{
			component: {
				name: "cl-form-card",
				props: {
					label: "其他信息",
					expand: true
				}
			},
			children: [
				{
					label: "团队/咨询",
					prop: "teamConsult",
					component: { name: "el-input", props: { clearable: true, placeholder: "如：团队/咨询" } },
					span: 12,
				},
				{
					label: "服务保障",
					prop: "serviceGuarantee",
					component: {
						name: "el-select",
						props: {
							multiple: true,
							filterable: true,
							allowCreate: true,
							placeholder: "请选择或输入添加服务保障"
						},
						options: options.serviceGuaranteeOptions.map(item => ({ label: item, value: item }))
					},
					span: 12,
				},
			]
		},

		// ============ 预定须知卡片 ============
		{
			component: {
				name: "cl-form-card",
				props: {
					label: "预定须知",
					expand: true
				}
			},
			children: [
				{
					label: "预定须知类型",
					prop: "bookingNoticeType",
					component: {
						name: "el-radio-group",
						options: options.bookingNoticeType,
					},
					value: 0,
					span: 24,
				},
				{
					label: "本单详情",
					prop: "thisOrderDetails",
					component: {
						name: "el-input",
						props: { clearable: true, placeholder: "请输入本单详情" },
					},
					hidden: ({ scope }) => scope.bookingNoticeType === 1,
					span: 12,
				},
				{
					label: "预订限制",
					prop: "bookingLimitations",
					component: {
						name: "el-input",
						props: { clearable: true, placeholder: "请输入预订限制" },
					},
					hidden: ({ scope }) => scope.bookingNoticeType === 1,
					span: 12,
				},
				{
					label: "营业时间",
					prop: "businessHours",
					component: { name: "el-input", props: { clearable: true, placeholder: "如：09:00-18:00" } },
					hidden: ({ scope }) => scope.bookingNoticeType === 1,
					span: 12,
				},
				{
					label: "取票地点",
					prop: "ticketLocation",
					component: { name: "el-input", props: { clearable: true, placeholder: "请输入取票地点" } },
					hidden: ({ scope }) => scope.bookingNoticeType === 1,
					span: 12,
				},
				{
					label: "取票方式",
					prop: "ticketMethod",
					component: {
						name: "el-input",
						props: { clearable: true, placeholder: "请输入取票方式" },
					},
					hidden: ({ scope }) => scope.bookingNoticeType === 1,
					span: 12,
				},
				{
					label: "退改规则",
					prop: "refundRules",
					component: {
						name: "el-input",
						props: { clearable: true, placeholder: "请输入退改规则" },
					},
					hidden: ({ scope }) => scope.bookingNoticeType === 1,
					span: 12,
				},
				{
					label: "发票说明",
					prop: "invoiceInfo",
					component: {
						name: "el-input",
						props: { clearable: true, placeholder: "请输入发票说明" },
					},
					hidden: ({ scope }) => scope.bookingNoticeType === 1,
					span: 12,
				},
				{
					label: "自费项目",
					prop: "selfPayItems",
					component: {
						name: "el-input",
						props: { clearable: true, placeholder: "请输入自费项目" },
					},
					hidden: ({ scope }) => scope.bookingNoticeType === 1,
					span: 12,
				},
				{
					label: "腕带说明",
					prop: "braceletInstructions",
					component: {
						name: "el-input",
						props: { clearable: true, placeholder: "请输入腕带说明" },
					},
					hidden: ({ scope }) => scope.bookingNoticeType === 1,
					span: 12,
				},
				{
					label: "自定义预定须知字段",
					prop: "customBookingNoticeFields",
					component: {
						name: "slot-custom-booking-notice"
					},
					value: [],
					hidden: ({ scope }) => scope.bookingNoticeType === 0,
				},
			]
		},

		// ============ 配置设置卡片 ============
		{
			component: {
				name: "cl-form-card",
				props: {
					label: "配置设置",
					expand: true
				}
			},
			children: [
				{
					label: "是否最受欢迎",
					prop: "isPopular",
					component: {
						name: "el-radio-group",
						options: options.isPopular,
					},
					value: 0,
					span: 12,
				},
				{
					label: "状态",
					prop: "status",
					component: { name: "el-radio-group", options: options.status },
					value: 1,
					span: 12,
				},
				{
					label: '行程附件上传',
					prop: 'itineraryAttachment',
					component: { name: 'cl-upload', props: { type: 'file', multiple: false, limit: 1 } },
					span: 12,
				},
			]
		},

		// ============ 产品详情 ============
		{
			component: {
				name: "cl-form-card",
				props: {
					label: "产品详情",
					expand: true
				}
			},
			children: [
				{
					label: "产品详情",
					prop: "productDetail",
					component: {
						name: "cl-editor-wang",
						props: {
							height: 300,
							placeholder: "请输入产品详情"
						},
					},
				},
				{
					label: "旅行路线",
					prop: "travelRoute",
					component: {
						name: "cl-editor-wang",
						props: {
							height: 300,
							placeholder: "请输入旅行路线"
						},
					},
				},
				{
					label: "其他方式",
					prop: "otherWays",
					component: {
						name: "cl-editor-wang",
						props: {
							height: 300,
							placeholder: "请输入其他方式"
						},
					},
				},
			]
		},
	],
	plugins: [
		async () => {
			// 每次打开表单时重新加载分类数据
			await loadCategories();
			console.log('表单插件执行，当前分类数量:', options.categoryOptions.length);
		}
	],

	// 表单打开后事件
	onOpened(data) {
		// 当编辑时，将 startDate 和 endDate 组合成 dateRange
		if (data.startDate && data.endDate) {
			data.dateRange = [data.startDate, data.endDate];
		}
	},

	// 表单提交前事件
	onSubmit(data, { next }) {
		// 提交前，将 dateRange 拆分成 startDate 和 endDate
		if (data.dateRange && Array.isArray(data.dateRange) && data.dateRange.length === 2) {
			data.startDate = data.dateRange[0];
			data.endDate = data.dateRange[1];
		}
		// 删除 dateRange 字段，避免提交到后端
		delete data.dateRange;

		// 继续提交
		next(data);
	}
});

// cl-table
const Table = useTable({
	columns: [
		{ type: "selection" },
		{ label: "产品编号", prop: "productCode", minWidth: 120 },
		{ label: "产品名称", prop: "name", minWidth: 200, showOverflowTooltip: true },
		{
			label: "产品类型",
			prop: "productType",
			minWidth: 100,
			dict: options.productType,
		},
		{
			label: "成团人数",
			prop: "groupSize",
			minWidth: 100,
			formatter: (row: any) => row.productType === 1 ? `${row.groupSize || 5}人` : '-',
		},
		{
			label: "价格",
			prop: "price",
			minWidth: 100,
			formatter: (row: any) => `¥${row.price}`,
		},
		{
			label: "原价",
			prop: "originalPrice",
			minWidth: 100,
			formatter: (row: any) => row.originalPrice ? `¥${row.originalPrice}` : '-',
		},
		{
			label: "评分",
			prop: "score",
			minWidth: 80,
			formatter: (row: any) => `${row.score}分`,
		},
		{
			label: "出发城市",
			prop: "city",
			minWidth: 100,
		},
		{
			label: "目的地",
			prop: "destination",
			minWidth: 120,
			showOverflowTooltip: true,
		},
		{
			label: "行程天数",
			prop: "days",
			minWidth: 90,
			formatter: (row: any) => `${row.days}天`,
		},
		{
			label: "分类",
			prop: "categoryNames",
			minWidth: 150,
		},
		{
			label: "封面图",
			prop: "cover",
			minWidth: 100,
			component: { name: "cl-image", props: { size: 60 } },
		},
		{
			label: "浏览次数",
			prop: "views",
			minWidth: 90,
			sortable: "custom",
		},
		{
			label: "预订次数",
			prop: "bookingCount",
			minWidth: 90,
			sortable: "custom",
		},
		{
			label: "热门",
			prop: "isHot",
			minWidth: 80,
			component: { name: "cl-switch" },
			dict: options.isHot,
		},
		{
			label: "最受欢迎",
			prop: "isPopular",
			minWidth: 100,
			component: { name: "cl-switch" },
			dict: options.isPopular,
		},
		{
			label: "状态",
			prop: "status",
			minWidth: 80,
			dict: options.status,
		},
		{
			label: "排序",
			prop: "sort",
			minWidth: 80,
			sortable: "custom",
		},
		{
			label: "创建时间",
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{
			label: "更新时间",
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ type: "op", buttons: ["edit", "delete"] },
	],
});

// cl-search
const Search = useSearch({
	items: [
	],
});

// cl-crud
const Crud = useCrud(
	{
		service: service.product.product,
	},
	(app) => {
		app.refresh();
	}
);

// 价格表管理方法
const initializePriceTable = (scope: any) => {
	scope.priceTable = {
		headers: [
			{ key: "peopleCount", label: "人数", type: "text" },
			{ key: "doubleRoom", label: "双人间", type: "text" },
			{ key: "singleRoom", label: "单人间", type: "text" },
			{ key: "extraRoom", label: "加床", type: "text" }
		],
		rows: [
			{ peopleCount: "10人", doubleRoom: "643", singleRoom: "857", extraRoom: "0" },
			{ peopleCount: "16人", doubleRoom: "551", singleRoom: "765", extraRoom: "0" },
			{ peopleCount: "25人", doubleRoom: "507", singleRoom: "721", extraRoom: "0" }
		]
	};
};

const addColumn = (scope: any) => {
	if (!scope.priceTable.headers) {
		scope.priceTable.headers = [];
	}
	const newKey = `column_${Date.now()}`;
	scope.priceTable.headers.push({
		key: newKey,
		label: "新列",
		type: "text"
	});

	// 为所有行添加新列的默认值
	if (scope.priceTable.rows) {
		scope.priceTable.rows.forEach((row: any) => {
			row[newKey] = "";
		});
	}
};

// 简化的添加列方法
const addColumnSimple = (scope: any) => {
	if (!scope.priceTable.headers) {
		scope.priceTable.headers = [];
	}

	// 检查是否超过最大列数限制
	if (scope.priceTable.headers.length >= 10) {
		return;
	}

	// 生成唯一的列标识
	const newKey = `col_${scope.priceTable.headers.length + 1}_${Date.now()}`;
	scope.priceTable.headers.push({
		key: newKey,
		label: `列${scope.priceTable.headers.length + 1}`,
		type: "text"
	});

	// 为所有现有行添加新列的默认值
	if (scope.priceTable.rows) {
		scope.priceTable.rows.forEach((row: any) => {
			row[newKey] = "";
		});
	}
};

const deleteColumn = (scope: any, columnIndex: number) => {
	if (!scope.priceTable.headers || columnIndex < 0 || columnIndex >= scope.priceTable.headers.length) {
		return;
	}

	const deletedKey = scope.priceTable.headers[columnIndex].key;

	// 删除列头
	scope.priceTable.headers.splice(columnIndex, 1);

	// 删除所有行中对应的列数据
	if (scope.priceTable.rows) {
		scope.priceTable.rows.forEach((row: any) => {
			delete row[deletedKey];
		});
	}
};

const addRow = (scope: any) => {
	if (!scope.priceTable.rows) {
		scope.priceTable.rows = [];
	}

	const newRow: any = {};
	if (scope.priceTable.headers) {
		scope.priceTable.headers.forEach((header: any) => {
			newRow[header.key] = '';
		});
	}

	scope.priceTable.rows.push(newRow);
};

const deleteRow = (scope: any, rowIndex: number) => {
	if (!scope.priceTable.rows || rowIndex < 0 || rowIndex >= scope.priceTable.rows.length) {
		return;
	}
	scope.priceTable.rows.splice(rowIndex, 1);
};

const convertOldFormat = (scope: any) => {
	if (!Array.isArray(scope.priceTable)) {
		return;
	}

	const oldData = scope.priceTable;
	scope.priceTable = {
		headers: [
			{ key: "peopleCount", label: "人数", type: "text" },
			{ key: "doubleRoom", label: "双人间", type: "text" },
			{ key: "singleRoom", label: "单人间", type: "text" },
			{ key: "extraRoom", label: "加床", type: "text" }
		],
		rows: oldData.map((item: any) => ({
			peopleCount: item.peopleCount || '',
			doubleRoom: String(item.doubleRoom || ''),
			singleRoom: String(item.singleRoom || ''),
			extraRoom: String(item.extraRoom || '')
		}))
	};
};

onMounted(async () => {
	// 预先加载分类数据
	await loadCategories();
	console.log('页面挂载完成，分类数据已加载:', options.categoryOptions);
});
</script>

<style scoped>
.price-table {
	padding: 15px;
	border: 1px solid #e4e7ed;
	border-radius: 4px;
	background-color: #f9f9f9;
}

.price-table .column-tag {
	display: flex;
	align-items: center;
	background: #f0f9ff;
	border: 1px solid #e0f2fe;
	border-radius: 4px;
	padding: 5px;
	margin: 2px;
}

.price-table .column-tag:hover {
	background: #e0f2fe;
	border-color: #0284c7;
}

.price-table .columns-container {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	align-items: center;
	min-height: 40px;
	padding: 8px;
	background: #fafafa;
	border-radius: 4px;
	border: 1px dashed #d1d5db;
}

.custom-itinerary {
	padding: 10px;
	border: 1px solid #e4e7ed;
	border-radius: 4px;
	background-color: #f9f9f9;
}

.custom-itinerary .el-table {
	margin-bottom: 10px;
}

.custom-booking-notice {
	padding: 10px;
	border: 1px solid #e4e7ed;
	border-radius: 4px;
	background-color: #f9f9f9;
}

.custom-booking-notice .el-table {
	margin-bottom: 10px;
}
</style>