import { BaseEntity } from '../../base/entity/base';
import { Column, Entity, Index, OneToMany } from 'typeorm';
import { Transform } from 'class-transformer';
import { Min, Max } from 'class-validator';
import { ProductProductCategoryEntity } from './product-category';

/**
 * 产品信息
 */
@Entity('product_product')
export class ProductProductEntity extends BaseEntity {
  @Column({ comment: '产品编号', length: 50, unique: true })
  productCode: string;

  @Column({ comment: '产品名称', length: 200 })
  name: string;

  @Column({ comment: '产品副标题', length: 500, nullable: true })
  subtitle: string;

  @Column({ comment: '产品介绍', type: 'text', nullable: true })
  introduce: string;

  @Column({ comment: '产品详情', type: 'longtext', nullable: true })
  productDetail: string;

  @Column({ comment: '旅行路线', type: 'longtext', nullable: true })
  travelRoute: string;

  @Column({ comment: '其他方式', type: 'longtext', nullable: true })
  otherWays: string;

  @Column({ comment: '开始日期', type: 'text', nullable: true })
  startDate: string;

  @Column({ comment: '结束日期', type: 'text', nullable: true })
  endDate: string;

  @Column({
    comment: '评分',
    type: 'decimal',
    precision: 2,
    scale: 1,
    default: 5,
  })
  @Min(0)
  @Max(5)
  score: number;

  @Column({ comment: '价格', type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ comment: '原价', type: 'decimal', precision: 10, scale: 2, nullable: true })
  originalPrice: number;

  @Column({ comment: '封面图', nullable: true })
  cover: string;

  @Column({ comment: '产品图片', type: 'simple-array', nullable: true })
  images: string[];

  @Column({ comment: '出发城市', length: 50, nullable: true })
  city: string;

  @Column({ comment: '目的地', length: 100, nullable: true })
  destination: string;

  @Column({ comment: '行程天数', type: 'int', default: 1 })
  days: number;

  @Index()
  @Column({ comment: '产品类型', dict: ['预定团', '散拼团'], default: 0 })
  productType: number;

  @Column({ comment: '成团人数', type: 'int', default: 5 })
  groupSize: number;

  @Column({ comment: '交通信息', length: 100, nullable: true })
  transport: string;

  @Column({ comment: '活动信息', length: 200, nullable: true })
  activityInfo: string;

  @Column({ comment: '团队/咨询', length: 200, nullable: true })
  teamConsult: string;

  @Column({ comment: '服务保障', type: 'simple-array', nullable: true })
  serviceGuarantee: string[];

  @Column({ comment: '行程信息类型', dict: ['预设字段', '自定义字段'], default: 0 })
  itineraryType: number;

  @Column({ comment: '自定义行程字段', type: 'json', nullable: true })
  customItineraryFields: any;

  @Column({ comment: '预定须知类型', dict: ['预设字段', '自定义字段'], default: 0 })
  bookingNoticeType: number;

  @Column({ comment: '自定义预定须知字段', type: 'json', nullable: true })
  customBookingNoticeFields: any;

  @Column({ comment: '本单详情', length: 500, nullable: true })
  thisOrderDetails: string;

  @Column({ comment: '预订限制', length: 500, nullable: true })
  bookingLimitations: string;

  @Column({ comment: '营业时间', length: 100, nullable: true })
  businessHours: string;

  @Column({ comment: '取票地点', length: 200, nullable: true })
  ticketLocation: string;

  @Column({ comment: '取票方式', type: 'text', nullable: true })
  ticketMethod: string;

  @Column({ comment: '退改规则', type: 'text', nullable: true })
  refundRules: string;

  @Column({ comment: '发票说明', type: 'text', nullable: true })
  invoiceInfo: string;

  @Column({ comment: '自费项目', type: 'text', nullable: true })
  selfPayItems: string;

  @Column({ comment: '腕带说明', type: 'text', nullable: true })
  braceletInstructions: string;

  @Column({ comment: '行程附件', nullable: true })
  itineraryAttachment: string;

  @Column({ comment: '价格表', type: 'json', nullable: true })
  priceTable: any;

  @Column({ comment: '浏览次数', type: 'int', default: 0 })
  views: number;

  @Column({ comment: '预订次数', type: 'int', default: 0 })
  bookingCount: number;

  // 产品分类关联
  @OneToMany(() => ProductProductCategoryEntity, relation => relation.product)
  productCategories: ProductProductCategoryEntity[];

  // 订单关联 - 一个产品可以有多个订单
  @OneToMany('OrderOrderEntity', 'product')
  orders: any[];

  @Index()
  @Column({ comment: '是否热门推荐', type: 'tinyint', default: 0 })
  @Transform(({ value }) => Boolean(value), { toPlainOnly: true })
  @Transform(({ value }) => (value ? 1 : 0), { toClassOnly: true })
  isHot: number;

  @Index()
  @Column({ comment: '最受欢迎状态', type: 'tinyint', default: 0 })
  @Transform(({ value }) => Boolean(value), { toPlainOnly: true })
  @Transform(({ value }) => (value ? 1 : 0), { toClassOnly: true })
  isPopular: number;

  @Index()
  @Column({ comment: '状态', dict: ['下架', '上架'], default: 1 })
  status: number;

  @Column({ comment: '排序', type: 'int', default: 0 })
  sort: number;
}
