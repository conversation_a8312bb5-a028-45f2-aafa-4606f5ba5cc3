import { Provide, Inject } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { ProductProductEntity } from '../entity/product';
import { ProductCategoryService } from './category';

/**
 * 产品
 */
@Provide()
export class ProductProductService extends BaseService {
  @InjectEntityModel(ProductProductEntity)
  productProductEntity: Repository<ProductProductEntity>;

  @Inject()
  productCategoryService: ProductCategoryService;

  /**
   * 重写删除方法，添加外键约束检查
   * @param ids 要删除的产品ID数组
   */
  async delete(ids: number[]) {
    // 确保ids是数组格式
    const productIds = Array.isArray(ids) ? ids : [ids];

    // 检查每个产品是否有关联的订单
    for (const productId of productIds) {
      // 检查是否有关联的订单
      const orderCount = await this.nativeQuery(
        'SELECT COUNT(*) as count FROM order_order WHERE productId = ?',
        [productId]
      );

      if (orderCount[0]?.count > 0) {
        // 获取产品名称用于错误提示
        const product = await this.productProductEntity.findOne({
          where: { id: productId },
          select: ['name', 'productCode']
        });

        const productName = product ? `${product.name}(${product.productCode})` : `ID:${productId}`;
        throw new Error(`无法删除产品"${productName}"，该产品已有关联的订单记录。请先处理相关订单后再删除。`);
      }

      // 检查是否有关联的拼团
      const groupCount = await this.nativeQuery(
        'SELECT COUNT(*) as count FROM order_group WHERE productId = ?',
        [productId]
      );

      if (groupCount[0]?.count > 0) {
        // 获取产品名称用于错误提示
        const product = await this.productProductEntity.findOne({
          where: { id: productId },
          select: ['name', 'productCode']
        });

        const productName = product ? `${product.name}(${product.productCode})` : `ID:${productId}`;
        throw new Error(`无法删除产品"${productName}"，该产品已有关联的拼团记录。请先处理相关拼团后再删除。`);
      }
    }

    // 如果没有外键约束，执行删除
    try {
      // 先删除产品分类关联
      for (const productId of productIds) {
        await this.productCategoryService.productProductCategoryEntity.delete({ productId });
      }

      // 再删除产品本身
      await this.productProductEntity.delete(productIds);
    } catch (error) {
      // 捕获其他可能的数据库错误
      throw new Error(`删除产品失败：${error.message}`);
    }
  }

  /**
   * 获取产品列表（前端业务页面）
   */
  async getProductList(params: {
    city?: string;
    categoryId?: number;
    theme?: string;
    page?: number;
    size?: number;
    isHot?: boolean;
  }) {
    const { city, categoryId, theme, page = 1, size = 10, isHot } = params;

    const queryBuilder = this.productProductEntity
      .createQueryBuilder('product')
      .where('product.status = :status', { status: 1 });

    // 城市筛选
    if (city && city !== '全部') {
      queryBuilder.andWhere('product.city = :city', { city });
    }

    // 分类筛选 - 使用关联表查询
    if (categoryId) {
      queryBuilder.innerJoin(
        'product_product_category',
        'pc',
        'pc.productId = product.id AND pc.categoryId = :categoryId',
        { categoryId }
      );
    }

    // 热门筛选
    if (isHot) {
      queryBuilder.andWhere('product.isHot = :isHot', { isHot: 1 });
    }

    // 排序
    queryBuilder.orderBy('product.sort', 'DESC')
      .addOrderBy('product.createTime', 'DESC');

    // 分页
    const [list, total] = await queryBuilder
      .skip((page - 1) * size)
      .take(size)
      .getManyAndCount();

    return {
      list,
      total,
      page,
      size
    };
  }

  /**
   * 获取产品详情
   */
  async getProductDetail(id: number) {
    const product = await this.productProductEntity.findOne({
      where: { id, status: 1 },
      relations: ['productCategories', 'productCategories.category']
    });

    if (product) {
      // 增加浏览次数
      await this.productProductEntity.update(id, {
        views: product.views + 1
      });
    }

    return product;
  }

  /**
   * 获取推荐产品
   */
  async getRecommendedProducts(limit: number = 6) {
    return await this.productProductEntity.find({
      where: { status: 1, isPopular: 1 },
      order: { sort: 'DESC', createTime: 'DESC' },
      take: limit
    });
  }

  /**
   * 根据分类ID获取产品
   */
  async getProductsByCategoryId(categoryId: number, limit: number = 10) {
    const queryBuilder = this.productProductEntity
      .createQueryBuilder('product')
      .innerJoin(
        'product_product_category',
        'pc',
        'pc.productId = product.id AND pc.categoryId = :categoryId',
        { categoryId }
      )
      .where('product.status = :status', { status: 1 });

    return await queryBuilder
      .orderBy('product.sort', 'DESC')
      .addOrderBy('product.createTime', 'DESC')
      .take(limit)
      .getMany();
  }

  /**
   * 搜索产品
   */
  async searchProducts(keyword: string, page: number = 1, size: number = 10) {
    const queryBuilder = this.productProductEntity
      .createQueryBuilder('product')
      .where('product.status = :status', { status: 1 })
      .andWhere(
        '(product.name LIKE :keyword OR product.subtitle LIKE :keyword OR product.introduce LIKE :keyword)',
        { keyword: `%${keyword}%` }
      );

    const [list, total] = await queryBuilder
      .orderBy('product.sort', 'DESC')
      .addOrderBy('product.createTime', 'DESC')
      .skip((page - 1) * size)
      .take(size)
      .getManyAndCount();

    return {
      list,
      total,
      page,
      size
    };
  }

  /**
   * 获取热门城市的产品
   */
  async getHotCityProducts() {
    return await this.productProductEntity
      .createQueryBuilder('product')
      .select('product.city, COUNT(*) as count')
      .where('product.status = :status', { status: 1 })
      .andWhere('product.city IS NOT NULL')
      .groupBy('product.city')
      .orderBy('count', 'DESC')
      .limit(10)
      .getRawMany();
  }

  /**
   * 获取所有城市列表（从product_product表中的city字段提取）
   */
  async getCityList() {
    const result = await this.productProductEntity
      .createQueryBuilder('product')
      .select('DISTINCT product.city as city')
      .where('product.status = :status', { status: 1 })
      .andWhere('product.city IS NOT NULL')
      .andWhere('product.city != :empty', { empty: '' })
      .orderBy('product.city', 'ASC')
      .getRawMany();

    return result.map(item => item.city);
  }

  /**
   * 保存产品（包含分类关联）
   */
  async saveProduct(data: any) {
    const { categoryIds, ...productData } = data;

    // 保存产品基本信息
    const product = await this.productProductEntity.save(productData);

    // 设置产品分类关联
    if (categoryIds && categoryIds.length > 0) {
      await this.productCategoryService.setProductCategories(product.id, categoryIds);
    }

    return product;
  }

  /**
   * 更新产品（包含分类关联）
   */
  async updateProduct(data: any) {
    const { id, categoryIds, ...productData } = data;

    // 更新产品基本信息
    await this.productProductEntity.update(id, productData);

    // 更新产品分类关联
    if (categoryIds !== undefined) {
      await this.productCategoryService.setProductCategories(id, categoryIds);
    }

    return await this.productProductEntity.findOne({ where: { id } });
  }
}
