import { get, post } from '../utils/request';

// 产品相关API
export const productApi = {
  // 获取产品列表
  getProductList: (params) => get('/app/product/list', params),
  
  // 获取产品详情
  getProductDetail: (id) => get('/app/product/detail', { id }),
  
  // 获取推荐产品
  getRecommendedProducts: (limit) => get('/app/product/recommended', { limit }),
  
  // 根据分类获取产品
  getProductsByCategory: (params) => get('/app/product/category', params),
  
  // 搜索产品
  searchProducts: (params) => get('/app/product/search', params),
  
  // 获取热门城市
  getHotCities: () => get('/app/product/hot-cities'),
  
  // 获取城市列表
  getCities: () => get('/app/product/cities'),
  
  // 获取产品统计信息
  getProductStats: () => get('/app/product/stats'),
};

// 分类相关API
export const categoryApi = {
  // 获取分类列表
  getCategoryList: (params) => get('/app/product/category/list', params),
  
  // 根据分类ID获取产品
  getProductsByCategoryId: (categoryId, params) => get('/app/product/categoryProducts', { categoryId, ...params }),
};

export default productApi; 